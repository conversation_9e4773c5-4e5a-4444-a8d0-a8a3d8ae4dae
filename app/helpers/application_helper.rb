module ApplicationHelper
  include Pagy::Frontend

  def projects_section_active?
    controller_name == 'projects' || 
    current_page?(projects_path) || 
    current_page?(show_my_projects_path) || 
    current_page?(new_project_path) ||
    params[:controller] == 'projects'
  end

  def network_section_active?
    controller_name == 'network_connections' || 
    controller_name == 'connection_requests' ||
    current_page?(network_connections_path) || 
    current_page?(my_network_connections_path) || 
    current_page?(invite_network_connections_path) ||
    params[:controller] == 'network_connections' ||
    params[:controller] == 'connection_requests'
  end

  def profile_section_active?
    controller_name == 'user_profiles' || 
    controller_name == 'registrations' ||
    params[:controller] == 'user_profiles' ||
    (params[:controller] == 'devise/registrations' && params[:action] == 'edit')
  end

  # Subscription tier badge helper methods
  def tier_badge_class(tier)
    case tier.to_s
    when 'free' then 'secondary'
    when 'standard' then 'success'
    when 'premium' then 'success'
    when 'pilot' then 'primary'
    else 'secondary'
    end
  end

  def status_badge_class(status)
    case status.to_s
    when 'active' then 'success'
    when 'expired' then 'warning'
    when 'used_up' then 'info'
    when 'disabled' then 'danger'
    else 'secondary'
    end
  end

  # Smart Slovak price formatting helper
  # Whole numbers: "1 000 000" (no decimals)
  # Decimal numbers: "1,50" (with decimals)
  def format_price(value)
    return "–" if value.nil?
    return value unless value.is_a?(Numeric)
    
    # Convert to BigDecimal for precise decimal detection
    decimal_value = value.is_a?(BigDecimal) ? value : BigDecimal(value.to_s)
    
    # Check if the number has decimal places
    if decimal_value == decimal_value.to_i
      # Whole number - format without decimals
      number_with_delimiter(decimal_value.to_i,
        delimiter: I18n.t('number.format.delimiter')
      )
    else
      # Has decimal places - format with decimals
      number_with_precision(decimal_value, 
        precision: 2, 
        strip_insignificant_zeros: false,  # Keep trailing zeros (1.50 → 1,50)
        separator: I18n.t('number.format.separator'),
        delimiter: I18n.t('number.format.delimiter')
      )
    end
  end

  # Format area units with proper superscript for squared meters
  def format_area_unit(unit)
    format_unit_with_superscript(unit)
  end

  # Format area display with proper unit formatting
  def format_area_display(area_value, area_unit)
    return t('common.not_specified', default: 'Nešpecifikované') if area_value.blank?
    
    if area_unit.present?
      "#{area_value} #{format_area_unit(area_unit)}".html_safe
    else
      area_value.to_s
    end
  end

  # Format price units with proper superscript for squared meters
  def format_price_unit(unit)
    format_unit_with_superscript(unit)
  end

  # Check if user should show verified badge (non-free tier users)
  def show_verified_badge?(user)
    user && !user.free_tier?
  end

  def user_name_with_badge(user)
    return "Unknown User" unless user

    name = h(user.full_name)
    badge = ""
    if show_verified_badge?(user)
      badge = heroicon("check-badge", variant: :solid, options: { class: "icon-20 text-primary ml-1" })
    end

    "#{name} #{badge}".html_safe
  end

  private

  def format_unit_with_superscript(unit)
    return unit if unit.blank?

    # Convert m2 or m² to proper HTML superscript
    unit.gsub(/m2\b/, 'm<sup>2</sup>')
        .gsub(/m²/, 'm<sup>2</sup>')
        .html_safe
  end

end
