<h1><%= t('wants.index.title') %></h1>
<div class="projects-filter">
  <%= form_tag wants_path(locale: I18n.locale), method: :get, class: 'filter-form' do %>
    <%= hidden_field_tag :locale, I18n.locale %>

    <div class="filter-controls">
      <div class="filter-row">
        <div class="filter-group">
          <div class="filter-group">
            <%= select_tag 'want_type', 
                options_for_select(Want.translated_want_types, params[:want_type]), 
                include_blank: t('wants.index.filters.want_type_placeholder'),   
                class: 'form-select',
                id: 'want_type_select' %>
          </div>
          <%
            # Get available categories for selected want type
            categories_options_for_select = Want.translated_categories_for(params[:want_type])
            
            # Ensure selected category is valid for the current want type
            selected_category_value = params[:category]
            if params[:want_type].present? && params[:category].present?
              valid_categories = Project::PROJECT_TYPES[params[:want_type]] || []
              unless valid_categories.include?(selected_category_value)
                selected_category_value = nil
              end  
            end
          %>
          <%= select_tag 'category', 
              options_for_select(categories_options_for_select, selected_category_value), 
              include_blank: t('wants.index.filters.category_placeholder'),   
              class: 'form-select',
              id: 'category_select' %>  
          <%= select_tag 'subcategory', 
              options_for_select([], params[:subcategory]), 
              include_blank: t('wants.index.filters.subcategory_placeholder'),   
              class: 'form-select',
              id: 'subcategory_select' %>
        </div>

        <div class="filter-group">
          <%= text_field_tag :search, params[:search], 
              placeholder: t('wants.index.filters.search_placeholder'), 
              class: 'form-input' %>
        </div>

        <div class="filter-group location-group">
          <%= text_field_tag :location, params[:location], 
              placeholder: t('wants.index.filters.location_placeholder'), 
              autocomplete: 'off',
              class: 'form-input' %>
          <%= hidden_field_tag :latitude, '', id: 'latitude' %>
          <%= hidden_field_tag :longitude, '', id: 'longitude' %>
          <%= hidden_field_tag :country, '', id: 'country' %>
          <%= hidden_field_tag :search_country_code, '', id: 'search_country_code' %>
          <%= hidden_field_tag :is_country_search, 'false', id: 'is_country_search' %>
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-actions">
          <%= submit_tag t('wants.index.filters.apply'), class: 'button' %>
          <%= link_to t('wants.index.filters.clear'), wants_path, class: 'text-link' %>
        </div>
      </div>
    </div>
  <% end %>
</div>
<div class="projects-list">
  <% if wants.present? %>
    <% wants.each do |want| %>
      <div class="project-item bg-white">
        
        <div class="project-main">
          <h3 class="project-title">
            <%= link_to want.summary, want_path(want) %>
          </h3>
          <div class="project-meta">
            <div class="project-location">
              <%= heroicon "map-pin", variant: :solid, options: { class: "icon-sm" } %>
              <span><%= want.place %></span>
            </div>
            <div class="want-author">
              <%= heroicon "user", variant: :solid, options: { class: "icon-sm" } %>
              <span><%= user_name_with_badge(want.user) %></span>
            </div>
          </div>
        </div>
        
        <div class="project-category">
          <div>
            <%= want.translated_want_type.humanize if want.want_type %>
          </div>
          <div>
            <%= want.translated_category.humanize if want.category %>
          </div>
          <div>
            <%= want.translated_subcategory.humanize if want.subcategory %>
          </div>
        </div>
        
        <div class="project-actions">
          <% if current_user == want.user %>
            <%= link_to t('common.actions.edit'), edit_want_path(want), class: "action-link" %>
          <% else %>
            <%= link_to t('common.actions.view'), want, class: "action-link" %>
          <% end %>
        </div>
        
        <div class="project-date">
          <%= l want.updated_at, format: :short_dmy %>
        </div>
      </div>
    <% end %>
  <% else %>
    <div class="no-wants">
      <p><%= t('wants.index.want_list.no_wants') %></p>
    </div>
  <% end %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const wantTypeSelect = document.getElementById('want_type_select');
    const categorySelect = document.getElementById('category_select');
    const subcategorySelect = document.getElementById('subcategory_select');
    
    const wantTypes = <%= raw @cached_want_types.to_json %>;
    const categories = <%= raw @cached_categories.to_json %>;
    const translations = {
      wantTypes: <%= raw @cached_translations[:wantTypes].to_json %>,
      categories: <%= raw @cached_translations[:categories].to_json %>,
      subcategories: <%= raw @cached_translations[:subcategories].to_json %>,
      placeholders: {
        category: '<%= j @cached_placeholders[:category] %>',
        subcategory: '<%= j @cached_placeholders[:subcategory] %>'
      }
    };
    
    const initialCategoryParam = '<%= j params[:category].to_s %>';
    const initialSubcategoryParam = '<%= j params[:subcategory].to_s %>';
    
    wantTypeSelect.addEventListener('change', function() {
      const selectedWantType = this.value;
      
      categorySelect.innerHTML = '<option value="">' + translations.placeholders.category + '</option>';
      subcategorySelect.innerHTML = '<option value="">' + translations.placeholders.subcategory + '</option>';
      
      if (selectedWantType && wantTypes[selectedWantType]) {
        wantTypes[selectedWantType].forEach(function(category) {
          const option = document.createElement('option');
          option.value = category;
          option.text = translations.categories[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
          categorySelect.appendChild(option);
        });
      }
    });
    
    categorySelect.addEventListener('change', function() {
      const selectedCategory = this.value;
      
      subcategorySelect.innerHTML = '<option value="">' + translations.placeholders.subcategory + '</option>';
      
      if (selectedCategory && categories[selectedCategory]) {
        categories[selectedCategory].forEach(function(subcategory) {
          const option = document.createElement('option');
          option.value = subcategory;
          option.text = translations.subcategories[subcategory] || subcategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
          subcategorySelect.appendChild(option);
        });
      }
    });
    
    if (wantTypeSelect.value) {
      wantTypeSelect.dispatchEvent(new Event('change'));
      
      if (initialCategoryParam) {
        categorySelect.value = initialCategoryParam;
      }
      
      setTimeout(() => {
        if (categorySelect.value) {
          categorySelect.dispatchEvent(new Event('change'));
          if (initialSubcategoryParam) {
            subcategorySelect.value = initialSubcategoryParam;
          }
        }
      }, 100); 
    }
  });
</script>