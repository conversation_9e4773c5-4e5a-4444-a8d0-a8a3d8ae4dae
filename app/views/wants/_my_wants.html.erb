<h1><%= t('wants.my.title') %></h1>
<div class="projects-filter">
  <div class="projects-list">
    <% if wants.present? %>
      <% wants.each do |want| %>
        <div class="project-item bg-white">
          <div class="project-main">
            <h3 class="project-title">
              <%= link_to want.summary.presence || t('wants.common.no_title'), want_path(want) %>
            </h3>
            <div class="project-meta">
              <div class="project-location">
                <%= heroicon "map-pin", variant: :solid, options: { class: "icon-sm" } %>
                <span><%= want.place %></span>
              </div>
            </div>
          </div>
          
          <div class="project-category">
            <div>
              <%= want.want_type ? want.translated_want_type.humanize : t('wants.common.draft') %>
            </div>
            <div>
              <%= want.category ? want.translated_category.humanize : '' %>
            </div>
            <div>
              <%= want.subcategory ? want.translated_subcategory.humanize : '' %>
            </div>
          </div>
          
          <div class="project-actions">
            <%= link_to t('common.actions.edit'), edit_want_path(want), class: "action-link" %>
          </div>
          
          <div class="project-date">
            <%= want.updated_at.strftime("%b %d '%y") %>
          </div>
        </div>
      <% end %>
    <% else %>
      <p><%= t('wants.index.empty') %></p>
    <% end %>
  </div>
</div>