<div class="projects-filter">
    <div class="filter-controls">
    </div>
</div>
<div class="projects-container">
  <% @projects_auths.group_by(&:id).each do |project_id, project_auths| %>
    <% project = project_auths.first %>
    
    <div class="project-card">
      <div class="project-header">
        <div class="project-visibility">
        </div>
      </div>

      <div class="project-content">
        <p class="project-location"><%= project.location %></p>
        <p class="text-gray-600 text-sm"><%= truncate(project.summary, length: 150) %></p>
      </div>

      <div class="auth-list mt-4">
        <h4 class="text-lg font-medium mb-2">
          <%= pluralize(project.project_auths.count, 'Authorized User') %>
        </h4>
        
        <div class="auth-users-grid">
          <% project.project_auths.each do |auth| %>
            <div class="auth-user-card">
              <div class="auth-user-info">
                <span class="auth-user-name">
                  <%= user_name_with_badge(auth.user) %>
                </span>
                
                <span class="access-badge access-badge--<%= auth.access_level %>">
                  <%= auth.access_level.humanize %>
                </span>
              </div>

              <%= button_to 'Delete', delete_access_project_path(auth), method: :delete, data: { confirm: 'Remove access to this project?' },
                            class: 'text-link red' %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
</div>