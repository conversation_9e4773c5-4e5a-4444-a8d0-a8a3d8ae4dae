<%# ABOUTME: Admin view for detailed subscription management for a specific user %>
<%# ABOUTME: Shows all subscriptions, allows manual creation, cancellation, and Stripe sync %>
<% content_for :title, "Subscription Management - #{@user.email}" %>

<div class="modern-admin-container">
  <header class="modern-admin-header">
    <div>
      <h1>Subscription Management</h1>
      <p>
        <strong><%= @user.email %></strong> 
        <% if @user.user_profile %>
          - <%= @user.user_profile.first_name %> <%= @user.user_profile.last_name %>
        <% end %>
      </p>
    </div>
    <%= link_to "← Back to Users", admin_subscriptions_path, class: "button" %>
  </header>

  <!-- Current Status Card -->
  <div class="modern-admin-card">
    <h2>Current Status</h2>
    <div class="status-info">
      <div class="info-row">
        <span class="label">User ID:</span>
        <span class="value"><%= @user.id %></span>
      </div>
      <div class="info-row">
        <span class="label">Stripe Customer ID:</span>
        <span class="value"><%= @user.stripe_customer_id || "Not connected" %></span>
      </div>
      <div class="info-row">
        <span class="label">Approval Status:</span>
        <span class="value">
          <span class="status-badge <%= @user.approved? ? 'status-approved' : 'status-pending' %>">
            <%= @user.approved? ? 'Approved' : 'Pending' %>
          </span>
        </span>
      </div>
      <div class="info-row">
        <span class="label">Active Subscription:</span>
        <span class="value">
          <% if @user.active_subscription %>
            <%= @user.active_subscription.plan.name %> 
            (<%= @user.active_subscription.status.humanize %>)
            <% if @user.active_subscription.payment_provider == 'manual' %>
              <span class="badge-manual">Manual</span>
            <% else %>
              <span class="badge-stripe">Stripe</span>
            <% end %>
          <% else %>
            <span style="color: #999;">None</span>
          <% end %>
        </span>
      </div>
    </div>
    
    <!-- Sync from Stripe Button -->
    <% if @user.stripe_customer_id.present? %>
      <div class="sync-section">
        <%= form_with url: sync_from_stripe_admin_subscription_path(@user), method: :post, local: true, class: "sync-form" do |f| %>
          <%= f.submit "🔄 Sync from Stripe", class: "button button-primary", 
              data: { confirm: "This will fetch and sync all subscriptions from Stripe for this customer. Continue?" } %>
          <p class="sync-help">Fetches all subscriptions from Stripe and updates local database</p>
        <% end %>
      </div>
    <% else %>
      <div class="sync-section">
        <p class="sync-help">⚠️ No Stripe Customer ID - cannot sync from Stripe</p>
      </div>
    <% end %>
  </div>

  <!-- Manual Subscription Creation -->
  <div class="modern-admin-card">
    <h2>Create Manual Subscription</h2>
    <%= form_with url: create_subscription_admin_subscription_path(@user), method: :post, local: true, class: "subscription-form" do |f| %>
      <div class="form-group">
        <%= f.label :plan_id, "Select Plan:" %>
        <%= f.select :plan_id, 
            options_for_select(@available_plans.map { |p| 
              ["#{p.name} - #{p.tier.humanize} (#{p.interval})", p.id] 
            }), 
            { prompt: "Choose a plan..." }, 
            class: "form-control" %>
      </div>
      <%= f.submit "Create Subscription", class: "button button-success" %>
      <p class="form-help">Creates a manual subscription without Stripe payment</p>
    <% end %>
  </div>

  <!-- Subscriptions List -->
  <div class="modern-admin-card">
    <h2>All Subscriptions</h2>
    <% if @subscriptions.any? %>
      <div class="subscriptions-list">
        <% @subscriptions.each do |subscription| %>
          <div class="subscription-item <%= subscription.active? ? 'active' : 'inactive' %>">
            <div class="subscription-header">
              <h3>
                <%= subscription.plan.name %>
                <span class="subscription-status <%= subscription.status %>">
                  <%= subscription.status.humanize %>
                </span>
              </h3>
              <div class="subscription-meta">
                <% if subscription.payment_provider == 'manual' %>
                  <span class="badge-manual">Manual</span>
                <% else %>
                  <span class="badge-stripe">Stripe</span>
                <% end %>
                <span class="subscription-id">ID: <%= subscription.id %></span>
              </div>
            </div>
            
            <div class="subscription-details">
              <div class="detail-row">
                <span class="label">Plan:</span>
                <span class="value"><%= subscription.plan.tier.humanize %> - <%= subscription.plan.interval %></span>
              </div>
              <div class="detail-row">
                <span class="label">Price:</span>
                <span class="value"><%= number_to_currency(subscription.plan.price_cents / 100.0, unit: 'EUR', format: "%n %u") %></span>
              </div>
              <div class="detail-row">
                <span class="label">Created:</span>
                <span class="value"><%= subscription.created_at.strftime('%B %d, %Y') %></span>
              </div>
              <% if subscription.stripe_subscription_id %>
                <div class="detail-row">
                  <span class="label">Stripe ID:</span>
                  <span class="value" style="font-family: monospace; font-size: 0.85em;">
                    <%= subscription.stripe_subscription_id %>
                  </span>
                </div>
              <% end %>
              <% if subscription.current_period_end %>
                <div class="detail-row">
                  <span class="label">
                    <% if subscription.cancel_at_period_end? %>
                      Cancels on:
                    <% else %>
                      Next billing:
                    <% end %>
                  </span>
                  <span class="value"><%= subscription.current_period_end.strftime('%B %d, %Y') %></span>
                </div>
              <% end %>
              <% if subscription.canceled_at %>
                <div class="detail-row">
                  <span class="label">Canceled:</span>
                  <span class="value"><%= subscription.canceled_at.strftime('%B %d, %Y') %></span>
                </div>
              <% end %>
            </div>
            
            <div class="subscription-actions">
              <% if subscription.active? || subscription.trialing? %>
                <%= form_with url: cancel_subscription_admin_subscription_path(@user), method: :patch, local: true, style: "display: inline-block;" do |f| %>
                  <%= f.hidden_field :subscription_id, value: subscription.id %>
                  <%= f.submit "Cancel", class: "button button-danger button-small",
                      data: { confirm: "Cancel this subscription?" } %>
                <% end %>
              <% elsif subscription.can_reactivate? %>
                <%= form_with url: reactivate_subscription_admin_subscription_path(@user), method: :patch, local: true, style: "display: inline-block;" do |f| %>
                  <%= f.hidden_field :subscription_id, value: subscription.id %>
                  <%= f.submit "Reactivate", class: "button button-success button-small" %>
                <% end %>
              <% end %>
              
              <% if !subscription.active? %>
                <%= form_with url: admin_subscription_path(@user, subscription_id: subscription.id), method: :delete, local: true, style: "display: inline-block;" do |f| %>
                  <%= f.submit "Delete Record", class: "button button-outline button-small",
                      data: { confirm: "Permanently delete this subscription record?" } %>
                <% end %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <p class="no-data">No subscriptions found for this user.</p>
    <% end %>
  </div>
</div>

<style>
.modern-admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.modern-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.modern-admin-header h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.modern-admin-header p {
  color: #6b7280;
  margin: 0.5rem 0 0 0;
}

.modern-admin-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.modern-admin-card h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #374151;
}

.status-info, .subscription-details {
  margin-bottom: 1rem;
}

.info-row, .detail-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-row:last-child, .detail-row:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #6b7280;
}

.value {
  color: #111827;
  text-align: right;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-approved {
  background: #d1fae5;
  color: #065f46;
}

.status-pending {
  background: #fed7aa;
  color: #92400e;
}

.badge-manual {
  display: inline-block;
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-stripe {
  display: inline-block;
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.sync-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.sync-help, .form-help {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.subscription-form {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.subscriptions-list {
  margin-top: 1rem;
}

.subscription-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  background: #f9fafb;
}

.subscription-item.active {
  background: #f0fdf4;
  border-color: #86efac;
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.subscription-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.subscription-status {
  display: inline-block;
  margin-left: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.subscription-status.active {
  background: #d1fae5;
  color: #065f46;
}

.subscription-status.canceled {
  background: #fee2e2;
  color: #991b1b;
}

.subscription-status.trialing {
  background: #dbeafe;
  color: #1e40af;
}

.subscription-status.past_due {
  background: #fef3c7;
  color: #92400e;
}

.subscription-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subscription-id {
  font-size: 0.75rem;
  color: #9ca3af;
}

.subscription-actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 0.5rem;
}

.button {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.button-primary {
  background: #3b82f6;
  color: white;
}

.button-primary:hover {
  background: #2563eb;
}

.button-success {
  background: #10b981;
  color: white;
}

.button-success:hover {
  background: #059669;
}

.button-danger {
  background: #ef4444;
  color: white;
}

.button-danger:hover {
  background: #dc2626;
}

.button-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.button-outline:hover {
  background: #f3f4f6;
}

.button-small {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.no-data {
  text-align: center;
  color: #9ca3af;
  padding: 2rem;
}
</style>