<div class="requests-container">
  <h1><%= t('common.titles.incoming_requests', default: 'Incoming Requests') %></h1>
  <div class="request-list">
    <% if @all_requests.present? %>
      <% @all_requests.each do |req| %>
        <div class="request-card">
          <div class="request-content">
            <div class="request-avatar <%= "project" if req.project %>">
              <%= req.inviter.user_profile.first_name.first.capitalize %>
            </div>
            
            <div class="request-info">
              <%#= link_to user_profile_connection_request_path(req.inviter.user_profile.id), class: 'requester-name modal-trigger' do %>
                <h2>
                  <%= user_name_with_badge(req.inviter) %>
                </h2>
              <%# end %>
              
              <div class="requester-location">
                <%= [req.inviter.user_profile.city, req.inviter.user_profile.country].reject(&:blank?).join(', ') %>
              </div>
              
              <div class="request-message">
                <%= truncate(req.message, length: 100, omission: '...') %>
                <%= link_to t('common.actions.show_more', default: 'Show more'),
                            connection_request_path(req),
                            class: 'modal-trigger' %>
              </div>
              
              <% if req.project %>
                <div class="requested-project">
                  <span class="project-label"><%= t('common.labels.deal_requested', default: 'Deal requested:') %></span>
                  <%= truncate(req.project.summary, length: 80) %>
                  <%= link_to "(#{t('common.texts.link_to_deal', default: 'link to deal')})",
                              project_path(req.project),
                              class: "" %>
                </div>
              <% end %>
              
              <div class="request-actions">
                <% if req.project %>
                  <% if req.project.user_id == current_user.id && req.status == "pending" %>
                  <%= button_to t('common.actions.grant_project_access', default: 'Grant Deal Access'), 
                    accept_auth_request_connection_request_path(req), 
                    method: :post, 
                    class: "action-button action-button--primary" %>
                  <%= button_to t('common.actions.reject', default: 'Reject'), 
                    reject_auth_request_connection_request_path(req), 
                    method: :post, 
                    class: "action-button action-button--reject", 
                    data: { confirm: t('common.confirmations.reject_request', default: 'Are you sure you want to reject this request?') } %>
                  <% end %>
                <% else %>
                  <%= button_to t('common.actions.accept', default: 'Accept'), 
                    accept_network_request_connection_request_path(req), 
                    method: :post, 
                    class: "action-button action-button--primary" %>
                  <%= button_to t('common.actions.reject', default: 'Reject'), 
                    reject_network_request_connection_request_path(req), 
                    method: :post, 
                    class: "action-button action-button--reject", 
                    data: { confirm: t('common.confirmations.reject_request', default: 'Are you sure you want to reject this request?') } %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>