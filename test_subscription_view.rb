#!/usr/bin/env ruby
# ABOUTME: Test script to debug subscription view data
# ABOUTME: Simulates the controller and view logic to find the issue

require_relative 'config/environment'

user = User.find(1)
puts "\n=== CONTROLLER SIMULATION ==="
puts "User ID: #{user.id}"
puts "User email: #{user.email}"

# Simulate controller logic
@current_subscription = user.active_subscription
@has_active_standard_subscription = user.has_paid_subscription?

puts "\n@has_active_standard_subscription: #{@has_active_standard_subscription}"
puts "@current_subscription present?: #{@current_subscription.present?}"

if @current_subscription
  puts "\n=== SUBSCRIPTION DATA ==="
  puts "ID: #{@current_subscription.id}"
  puts "Status: #{@current_subscription.status}"
  puts "Plan name: #{@current_subscription.plan.name}"
  puts "Plan interval: #{@current_subscription.plan.interval}"
  puts "cancel_at_period_end: #{@current_subscription.cancel_at_period_end.inspect}"
  puts "current_period_end: #{@current_subscription.current_period_end.inspect}"
  puts "current_period_end class: #{@current_subscription.current_period_end.class}"

  puts "\n=== VIEW LOGIC SIMULATION ==="
  puts "First condition (@has_active_standard_subscription && @current_subscription): #{@has_active_standard_subscription && @current_subscription}"

  if @has_active_standard_subscription && @current_subscription
    puts "✓ Would show current-subscription-info div"

    if @current_subscription.cancel_at_period_end
      puts "  → Would show expires warning"
    elsif @current_subscription.current_period_end
      puts "  → Would show renewal date: #{@current_subscription.current_period_end.strftime('%B %d, %Y')}"
    else
      puts "  → NO DATE WOULD BE SHOWN (neither condition met)"
    end
  else
    puts "✗ Would NOT show current-subscription-info div"
  end
end

puts "\n=== CHECKING DATABASE DIRECTLY ==="
subscription = Subscription.find(4)
puts "Direct DB query - Subscription ID 4:"
puts "  cancel_at_period_end: #{subscription.cancel_at_period_end.inspect}"
puts "  current_period_end: #{subscription.current_period_end.inspect}"
puts "  current_period_end present?: #{subscription.current_period_end.present?}"