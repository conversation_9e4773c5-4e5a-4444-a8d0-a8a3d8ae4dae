require 'rails_helper'

RSpec.describe ApplicationHelper, type: :helper do
  describe '#user_name_with_badge' do
    let(:user) { create(:user) }
    
    before do
      # Ensure user has a complete profile
      user.user_profile.update!(
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>'
      )
    end
    
    context 'when user is nil' do
      it 'returns "Unknown User"' do
        expect(helper.user_name_with_badge(nil)).to eq('Unknown User')
      end
    end
    
    context 'when user is free tier' do
      before do
        allow(user).to receive(:free_tier?).and_return(true)
      end
      
      it 'returns just the name without badge' do
        result = helper.user_name_with_badge(user)
        expect(result).to eq('<PERSON>')
        expect(result).not_to include('check-badge')
      end
    end
    
    context 'when user has paid subscription' do
      before do
        allow(user).to receive(:free_tier?).and_return(false)
      end
      
      it 'returns name with badge' do
        result = helper.user_name_with_badge(user)
        expect(result).to include('<PERSON> Doe')
        expect(result).to include('<svg')
        expect(result).to include('icon-16')
        expect(result).to include('text-primary')
      end
      
      it 'returns html_safe string' do
        result = helper.user_name_with_badge(user)
        expect(result).to be_html_safe
      end
    end
  end
  
  describe '#show_verified_badge?' do
    let(:user) { create(:user) }
    
    context 'when user is nil' do
      it 'returns false' do
        expect(helper.show_verified_badge?(nil)).to be false
      end
    end
    
    context 'when user is free tier' do
      before do
        allow(user).to receive(:free_tier?).and_return(true)
      end
      
      it 'returns false' do
        expect(helper.show_verified_badge?(user)).to be false
      end
    end
    
    context 'when user has paid subscription' do
      before do
        allow(user).to receive(:free_tier?).and_return(false)
      end
      
      it 'returns true' do
        expect(helper.show_verified_badge?(user)).to be true
      end
    end
  end
end
